{"version": 3, "sources": [], "sections": [{"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/sandbox/src/components/BeforeAfterSlider.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useRef, useEffect } from 'react';\n\ninterface BeforeAfterSliderProps {\n  beforeImage: string;\n  afterImage: string;\n  beforeLabel?: string;\n  afterLabel?: string;\n  'data-oid'?: string;\n}\n\nexport default function BeforeAfterSlider({\n  beforeImage,\n  afterImage,\n  beforeLabel = \"Before\",\n  afterLabel = \"After\",\n  'data-oid': dataOid\n}: BeforeAfterSliderProps) {\n  const [sliderPosition, setSliderPosition] = useState(50);\n  const [isDragging, setIsDragging] = useState(false);\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  const handleMouseDown = (e: React.MouseEvent) => {\n    setIsDragging(true);\n    updateSliderPosition(e.clientX);\n  };\n\n  const handleMouseMove = (e: MouseEvent) => {\n    if (isDragging) {\n      updateSliderPosition(e.clientX);\n    }\n  };\n\n  const handleMouseUp = () => {\n    setIsDragging(false);\n  };\n\n  const handleTouchStart = (e: React.TouchEvent) => {\n    setIsDragging(true);\n    updateSliderPosition(e.touches[0].clientX);\n  };\n\n  const handleTouchMove = (e: TouchEvent) => {\n    if (isDragging) {\n      e.preventDefault();\n      updateSliderPosition(e.touches[0].clientX);\n    }\n  };\n\n  const handleTouchEnd = () => {\n    setIsDragging(false);\n  };\n\n  const updateSliderPosition = (clientX: number) => {\n    if (!containerRef.current) return;\n\n    const rect = containerRef.current.getBoundingClientRect();\n    const x = clientX - rect.left;\n    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));\n    setSliderPosition(percentage);\n  };\n\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n      document.addEventListener('touchmove', handleTouchMove, { passive: false });\n      document.addEventListener('touchend', handleTouchEnd);\n    }\n\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseup', handleMouseUp);\n      document.removeEventListener('touchmove', handleTouchMove);\n      document.removeEventListener('touchend', handleTouchEnd);\n    };\n  }, [isDragging]);\n\n  return (\n    <div\n      ref={containerRef}\n      className=\"relative w-full aspect-[4/3] overflow-hidden rounded-lg border border-gray-200 bg-gray-100 cursor-col-resize select-none focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2\"\n      tabIndex={0}\n      role=\"slider\"\n      aria-label=\"Before and after comparison slider\"\n      aria-valuemin={0}\n      aria-valuemax={100}\n      aria-valuenow={sliderPosition}\n      onKeyDown={(e) => {\n        if (e.key === 'ArrowLeft') {\n          e.preventDefault();\n          setSliderPosition(Math.max(0, sliderPosition - 5));\n        } else if (e.key === 'ArrowRight') {\n          e.preventDefault();\n          setSliderPosition(Math.min(100, sliderPosition + 5));\n        }\n      }}\n      data-oid={dataOid}>\n\n      {/* Before Image (Background) */}\n      <div\n        className=\"absolute inset-0\"\n        data-oid={`${dataOid}-before-container`}>\n        <img\n          src={beforeImage}\n          alt={`${beforeLabel} - carpet cleaning before image`}\n          className=\"w-full h-full object-cover\"\n          draggable={false}\n          data-oid={`${dataOid}-before-image`}\n        />\n        {/* Before Label */}\n        <div\n          className=\"absolute top-4 left-4 bg-red-600/90 text-white px-3 py-1 rounded-md text-sm font-medium backdrop-blur-sm shadow-sm\"\n          data-oid={`${dataOid}-before-label`}>\n          {beforeLabel}\n        </div>\n      </div>\n\n      {/* After Image (Clipped) */}\n      <div\n        className=\"absolute inset-0\"\n        style={{\n          clipPath: `inset(0 ${100 - sliderPosition}% 0 0)`\n        }}\n        data-oid={`${dataOid}-after-container`}>\n        <img\n          src={afterImage}\n          alt={`${afterLabel} - carpet cleaning after image`}\n          className=\"w-full h-full object-cover\"\n          draggable={false}\n          data-oid={`${dataOid}-after-image`}\n        />\n        {/* After Label */}\n        <div\n          className=\"absolute top-4 right-4 bg-green-600/90 text-white px-3 py-1 rounded-md text-sm font-medium backdrop-blur-sm shadow-sm\"\n          data-oid={`${dataOid}-after-label`}>\n          {afterLabel}\n        </div>\n      </div>\n\n      {/* Slider Handle */}\n      <div\n        className=\"absolute top-0 bottom-0 w-1 bg-white shadow-lg cursor-col-resize z-10\"\n        style={{ left: `${sliderPosition}%`, transform: 'translateX(-50%)' }}\n        onMouseDown={handleMouseDown}\n        onTouchStart={handleTouchStart}\n        data-oid={`${dataOid}-slider-line`}>\n        \n        {/* Slider Button */}\n        <div\n          className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white rounded-full shadow-lg border-2 border-gray-300 flex items-center justify-center cursor-col-resize hover:border-teal-500 transition-colors\"\n          data-oid={`${dataOid}-slider-handle`}>\n          \n          {/* Left Arrow */}\n          <svg\n            className=\"w-3 h-3 text-gray-600 absolute left-1\"\n            fill=\"currentColor\"\n            viewBox=\"0 0 20 20\"\n            data-oid={`${dataOid}-left-arrow`}>\n            <path\n              fillRule=\"evenodd\"\n              d=\"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\"\n              clipRule=\"evenodd\"\n            />\n          </svg>\n          \n          {/* Right Arrow */}\n          <svg\n            className=\"w-3 h-3 text-gray-600 absolute right-1\"\n            fill=\"currentColor\"\n            viewBox=\"0 0 20 20\"\n            data-oid={`${dataOid}-right-arrow`}>\n            <path\n              fillRule=\"evenodd\"\n              d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\"\n              clipRule=\"evenodd\"\n            />\n          </svg>\n        </div>\n      </div>\n\n      {/* Instructions overlay (shows on hover) */}\n      <div\n        className=\"absolute inset-0 bg-black/20 opacity-0 hover:opacity-100 transition-opacity duration-300 flex items-center justify-center pointer-events-none\"\n        data-oid={`${dataOid}-instructions`}>\n        <div className=\"bg-white/90 backdrop-blur-sm px-4 py-2 rounded-lg text-sm font-medium text-gray-900 shadow-sm\">\n          <span className=\"hidden sm:inline\">Drag to compare</span>\n          <span className=\"sm:hidden\">Tap and drag to compare</span>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAYe,SAAS,kBAAkB,EACxC,WAAW,EACX,UAAU,EACV,cAAc,QAAQ,EACtB,aAAa,OAAO,EACpB,YAAY,OAAO,EACI;IACvB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,iNAAQ,EAAC;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAC7C,MAAM,eAAe,IAAA,+MAAM,EAAiB;IAE5C,MAAM,kBAAkB,CAAC;QACvB,cAAc;QACd,qBAAqB,EAAE,OAAO;IAChC;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,YAAY;YACd,qBAAqB,EAAE,OAAO;QAChC;IACF;IAEA,MAAM,gBAAgB;QACpB,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,cAAc;QACd,qBAAqB,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO;IAC3C;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,YAAY;YACd,EAAE,cAAc;YAChB,qBAAqB,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO;QAC3C;IACF;IAEA,MAAM,iBAAiB;QACrB,cAAc;IAChB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,CAAC,aAAa,OAAO,EAAE;QAE3B,MAAM,OAAO,aAAa,OAAO,CAAC,qBAAqB;QACvD,MAAM,IAAI,UAAU,KAAK,IAAI;QAC7B,MAAM,aAAa,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,AAAC,IAAI,KAAK,KAAK,GAAI;QAChE,kBAAkB;IACpB;IAEA,IAAA,kNAAS,EAAC;QACR,IAAI,YAAY;YACd,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,gBAAgB,CAAC,WAAW;YACrC,SAAS,gBAAgB,CAAC,aAAa,iBAAiB;gBAAE,SAAS;YAAM;YACzE,SAAS,gBAAgB,CAAC,YAAY;QACxC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,mBAAmB,CAAC,YAAY;QAC3C;IACF,GAAG;QAAC;KAAW;IAEf,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;QACV,UAAU;QACV,MAAK;QACL,cAAW;QACX,iBAAe;QACf,iBAAe;QACf,iBAAe;QACf,WAAW,CAAC;YACV,IAAI,EAAE,GAAG,KAAK,aAAa;gBACzB,EAAE,cAAc;gBAChB,kBAAkB,KAAK,GAAG,CAAC,GAAG,iBAAiB;YACjD,OAAO,IAAI,EAAE,GAAG,KAAK,cAAc;gBACjC,EAAE,cAAc;gBAChB,kBAAkB,KAAK,GAAG,CAAC,KAAK,iBAAiB;YACnD;QACF;QACA,YAAU;;0BAGV,8OAAC;gBACC,WAAU;gBACV,YAAU,GAAG,QAAQ,iBAAiB,CAAC;;kCACvC,8OAAC;wBACC,KAAK;wBACL,KAAK,GAAG,YAAY,+BAA+B,CAAC;wBACpD,WAAU;wBACV,WAAW;wBACX,YAAU,GAAG,QAAQ,aAAa,CAAC;;;;;;kCAGrC,8OAAC;wBACC,WAAU;wBACV,YAAU,GAAG,QAAQ,aAAa,CAAC;kCAClC;;;;;;;;;;;;0BAKL,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,UAAU,CAAC,QAAQ,EAAE,MAAM,eAAe,MAAM,CAAC;gBACnD;gBACA,YAAU,GAAG,QAAQ,gBAAgB,CAAC;;kCACtC,8OAAC;wBACC,KAAK;wBACL,KAAK,GAAG,WAAW,8BAA8B,CAAC;wBAClD,WAAU;wBACV,WAAW;wBACX,YAAU,GAAG,QAAQ,YAAY,CAAC;;;;;;kCAGpC,8OAAC;wBACC,WAAU;wBACV,YAAU,GAAG,QAAQ,YAAY,CAAC;kCACjC;;;;;;;;;;;;0BAKL,8OAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,MAAM,GAAG,eAAe,CAAC,CAAC;oBAAE,WAAW;gBAAmB;gBACnE,aAAa;gBACb,cAAc;gBACd,YAAU,GAAG,QAAQ,YAAY,CAAC;0BAGlC,cAAA,8OAAC;oBACC,WAAU;oBACV,YAAU,GAAG,QAAQ,cAAc,CAAC;;sCAGpC,8OAAC;4BACC,WAAU;4BACV,MAAK;4BACL,SAAQ;4BACR,YAAU,GAAG,QAAQ,WAAW,CAAC;sCACjC,cAAA,8OAAC;gCACC,UAAS;gCACT,GAAE;gCACF,UAAS;;;;;;;;;;;sCAKb,8OAAC;4BACC,WAAU;4BACV,MAAK;4BACL,SAAQ;4BACR,YAAU,GAAG,QAAQ,YAAY,CAAC;sCAClC,cAAA,8OAAC;gCACC,UAAS;gCACT,GAAE;gCACF,UAAS;;;;;;;;;;;;;;;;;;;;;;0BAOjB,8OAAC;gBACC,WAAU;gBACV,YAAU,GAAG,QAAQ,aAAa,CAAC;0BACnC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAmB;;;;;;sCACnC,8OAAC;4BAAK,WAAU;sCAAY;;;;;;;;;;;;;;;;;;;;;;;AAKtC", "debugId": null}}]}