{"version": 3, "sources": [], "sections": [{"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/sandbox/src/components/BeforeAfterSlider.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useRef, useEffect } from 'react';\n\ninterface BeforeAfterSliderProps {\n  beforeImage: string;\n  afterImage: string;\n  beforeLabel?: string;\n  afterLabel?: string;\n  'data-oid'?: string;\n}\n\nexport default function BeforeAfterSlider({\n  beforeImage,\n  afterImage,\n  beforeLabel = \"Before\",\n  afterLabel = \"After\",\n  'data-oid': dataOid\n}: BeforeAfterSliderProps) {\n  const [sliderPosition, setSliderPosition] = useState(50);\n  const [isDragging, setIsDragging] = useState(false);\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  const handleMouseDown = (e: React.MouseEvent) => {\n    setIsDragging(true);\n    updateSliderPosition(e.clientX);\n  };\n\n  const handleMouseMove = (e: MouseEvent) => {\n    if (isDragging) {\n      updateSliderPosition(e.clientX);\n    }\n  };\n\n  const handleMouseUp = () => {\n    setIsDragging(false);\n  };\n\n  const handleTouchStart = (e: React.TouchEvent) => {\n    setIsDragging(true);\n    updateSliderPosition(e.touches[0].clientX);\n  };\n\n  const handleTouchMove = (e: TouchEvent) => {\n    if (isDragging) {\n      e.preventDefault();\n      updateSliderPosition(e.touches[0].clientX);\n    }\n  };\n\n  const handleTouchEnd = () => {\n    setIsDragging(false);\n  };\n\n  const updateSliderPosition = (clientX: number) => {\n    if (!containerRef.current) return;\n\n    const rect = containerRef.current.getBoundingClientRect();\n    const x = clientX - rect.left;\n    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));\n    setSliderPosition(percentage);\n  };\n\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n      document.addEventListener('touchmove', handleTouchMove, { passive: false });\n      document.addEventListener('touchend', handleTouchEnd);\n    }\n\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseup', handleMouseUp);\n      document.removeEventListener('touchmove', handleTouchMove);\n      document.removeEventListener('touchend', handleTouchEnd);\n    };\n  }, [isDragging]);\n\n  return (\n    <div\n      ref={containerRef}\n      className=\"relative w-full aspect-[4/3] overflow-hidden rounded-lg border border-gray-200 bg-gray-100 cursor-col-resize select-none focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2\"\n      tabIndex={0}\n      role=\"slider\"\n      aria-label=\"Before and after comparison slider\"\n      aria-valuemin={0}\n      aria-valuemax={100}\n      aria-valuenow={sliderPosition}\n      onKeyDown={(e) => {\n        if (e.key === 'ArrowLeft') {\n          e.preventDefault();\n          setSliderPosition(Math.max(0, sliderPosition - 5));\n        } else if (e.key === 'ArrowRight') {\n          e.preventDefault();\n          setSliderPosition(Math.min(100, sliderPosition + 5));\n        }\n      }}\n      data-oid={dataOid}>\n\n      {/* Before Image (Background) */}\n      <div\n        className=\"absolute inset-0\"\n        data-oid={`${dataOid}-before-container`}>\n        <img\n          src={beforeImage}\n          alt={`${beforeLabel} - carpet cleaning before image`}\n          className=\"w-full h-full object-cover\"\n          draggable={false}\n          data-oid={`${dataOid}-before-image`}\n        />\n        {/* Before Label */}\n        <div\n          className=\"absolute top-4 left-4 bg-red-600/90 text-white px-3 py-1 rounded-md text-sm font-medium backdrop-blur-sm shadow-sm\"\n          data-oid={`${dataOid}-before-label`}>\n          {beforeLabel}\n        </div>\n      </div>\n\n      {/* After Image (Clipped) */}\n      <div\n        className=\"absolute inset-0\"\n        style={{\n          clipPath: `inset(0 ${100 - sliderPosition}% 0 0)`\n        }}\n        data-oid={`${dataOid}-after-container`}>\n        <img\n          src={afterImage}\n          alt={`${afterLabel} - carpet cleaning after image`}\n          className=\"w-full h-full object-cover\"\n          draggable={false}\n          data-oid={`${dataOid}-after-image`}\n        />\n        {/* After Label */}\n        <div\n          className=\"absolute top-4 right-4 bg-green-600/90 text-white px-3 py-1 rounded-md text-sm font-medium backdrop-blur-sm shadow-sm\"\n          data-oid={`${dataOid}-after-label`}>\n          {afterLabel}\n        </div>\n      </div>\n\n      {/* Slider Handle */}\n      <div\n        className=\"absolute top-0 bottom-0 w-1 bg-white shadow-lg cursor-col-resize z-10\"\n        style={{ left: `${sliderPosition}%`, transform: 'translateX(-50%)' }}\n        onMouseDown={handleMouseDown}\n        onTouchStart={handleTouchStart}\n        data-oid={`${dataOid}-slider-line`}>\n        \n        {/* Slider Button */}\n        <div\n          className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white rounded-full shadow-lg border-2 border-gray-300 flex items-center justify-center cursor-col-resize hover:border-teal-500 transition-colors\"\n          data-oid={`${dataOid}-slider-handle`}>\n          \n          {/* Left Arrow */}\n          <svg\n            className=\"w-3 h-3 text-gray-600 absolute left-1\"\n            fill=\"currentColor\"\n            viewBox=\"0 0 20 20\"\n            data-oid={`${dataOid}-left-arrow`}>\n            <path\n              fillRule=\"evenodd\"\n              d=\"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\"\n              clipRule=\"evenodd\"\n            />\n          </svg>\n          \n          {/* Right Arrow */}\n          <svg\n            className=\"w-3 h-3 text-gray-600 absolute right-1\"\n            fill=\"currentColor\"\n            viewBox=\"0 0 20 20\"\n            data-oid={`${dataOid}-right-arrow`}>\n            <path\n              fillRule=\"evenodd\"\n              d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\"\n              clipRule=\"evenodd\"\n            />\n          </svg>\n        </div>\n      </div>\n\n      {/* Instructions overlay (shows on hover) */}\n      <div\n        className=\"absolute inset-0 bg-black/20 opacity-0 hover:opacity-100 transition-opacity duration-300 flex items-center justify-center pointer-events-none\"\n        data-oid={`${dataOid}-instructions`}>\n        <div className=\"bg-white/90 backdrop-blur-sm px-4 py-2 rounded-lg text-sm font-medium text-gray-900 shadow-sm\">\n          <span className=\"hidden sm:inline\">Drag to compare</span>\n          <span className=\"sm:hidden\">Tap and drag to compare</span>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAYe,SAAS,kBAAkB,EACxC,WAAW,EACX,UAAU,EACV,cAAc,QAAQ,EACtB,aAAa,OAAO,EACpB,YAAY,OAAO,EACI;IACvB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,iNAAQ,EAAC;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAC7C,MAAM,eAAe,IAAA,+MAAM,EAAiB;IAE5C,MAAM,kBAAkB,CAAC;QACvB,cAAc;QACd,qBAAqB,EAAE,OAAO;IAChC;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,YAAY;YACd,qBAAqB,EAAE,OAAO;QAChC;IACF;IAEA,MAAM,gBAAgB;QACpB,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,cAAc;QACd,qBAAqB,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO;IAC3C;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,YAAY;YACd,EAAE,cAAc;YAChB,qBAAqB,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO;QAC3C;IACF;IAEA,MAAM,iBAAiB;QACrB,cAAc;IAChB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,CAAC,aAAa,OAAO,EAAE;QAE3B,MAAM,OAAO,aAAa,OAAO,CAAC,qBAAqB;QACvD,MAAM,IAAI,UAAU,KAAK,IAAI;QAC7B,MAAM,aAAa,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,AAAC,IAAI,KAAK,KAAK,GAAI;QAChE,kBAAkB;IACpB;IAEA,IAAA,kNAAS,EAAC;QACR,IAAI,YAAY;YACd,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,gBAAgB,CAAC,WAAW;YACrC,SAAS,gBAAgB,CAAC,aAAa,iBAAiB;gBAAE,SAAS;YAAM;YACzE,SAAS,gBAAgB,CAAC,YAAY;QACxC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,mBAAmB,CAAC,YAAY;QAC3C;IACF,GAAG;QAAC;KAAW;IAEf,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;QACV,UAAU;QACV,MAAK;QACL,cAAW;QACX,iBAAe;QACf,iBAAe;QACf,iBAAe;QACf,WAAW,CAAC;YACV,IAAI,EAAE,GAAG,KAAK,aAAa;gBACzB,EAAE,cAAc;gBAChB,kBAAkB,KAAK,GAAG,CAAC,GAAG,iBAAiB;YACjD,OAAO,IAAI,EAAE,GAAG,KAAK,cAAc;gBACjC,EAAE,cAAc;gBAChB,kBAAkB,KAAK,GAAG,CAAC,KAAK,iBAAiB;YACnD;QACF;QACA,YAAU;;0BAGV,8OAAC;gBACC,WAAU;gBACV,YAAU,GAAG,QAAQ,iBAAiB,CAAC;;kCACvC,8OAAC;wBACC,KAAK;wBACL,KAAK,GAAG,YAAY,+BAA+B,CAAC;wBACpD,WAAU;wBACV,WAAW;wBACX,YAAU,GAAG,QAAQ,aAAa,CAAC;;;;;;kCAGrC,8OAAC;wBACC,WAAU;wBACV,YAAU,GAAG,QAAQ,aAAa,CAAC;kCAClC;;;;;;;;;;;;0BAKL,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,UAAU,CAAC,QAAQ,EAAE,MAAM,eAAe,MAAM,CAAC;gBACnD;gBACA,YAAU,GAAG,QAAQ,gBAAgB,CAAC;;kCACtC,8OAAC;wBACC,KAAK;wBACL,KAAK,GAAG,WAAW,8BAA8B,CAAC;wBAClD,WAAU;wBACV,WAAW;wBACX,YAAU,GAAG,QAAQ,YAAY,CAAC;;;;;;kCAGpC,8OAAC;wBACC,WAAU;wBACV,YAAU,GAAG,QAAQ,YAAY,CAAC;kCACjC;;;;;;;;;;;;0BAKL,8OAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,MAAM,GAAG,eAAe,CAAC,CAAC;oBAAE,WAAW;gBAAmB;gBACnE,aAAa;gBACb,cAAc;gBACd,YAAU,GAAG,QAAQ,YAAY,CAAC;0BAGlC,cAAA,8OAAC;oBACC,WAAU;oBACV,YAAU,GAAG,QAAQ,cAAc,CAAC;;sCAGpC,8OAAC;4BACC,WAAU;4BACV,MAAK;4BACL,SAAQ;4BACR,YAAU,GAAG,QAAQ,WAAW,CAAC;sCACjC,cAAA,8OAAC;gCACC,UAAS;gCACT,GAAE;gCACF,UAAS;;;;;;;;;;;sCAKb,8OAAC;4BACC,WAAU;4BACV,MAAK;4BACL,SAAQ;4BACR,YAAU,GAAG,QAAQ,YAAY,CAAC;sCAClC,cAAA,8OAAC;gCACC,UAAS;gCACT,GAAE;gCACF,UAAS;;;;;;;;;;;;;;;;;;;;;;0BAOjB,8OAAC;gBACC,WAAU;gBACV,YAAU,GAAG,QAAQ,aAAa,CAAC;0BACnC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAmB;;;;;;sCACnC,8OAAC;4BAAK,WAAU;sCAAY;;;;;;;;;;;;;;;;;;;;;;;AAKtC", "debugId": null}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/sandbox/src/app/gallery/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport Link from \"next/link\";\nimport <PERSON> from \"next/head\";\nimport BeforeAfterSlider from \"@/components/BeforeAfterSlider\";\n\n// Simple, composable primitives matching homepage style\nconst Container = ({\n  children,\n  className = \"\"\n}: {children: React.ReactNode;className?: string;}) =>\n<div\n  className={`mx-auto w-full max-w-6xl px-4 sm:px-6 lg:px-8 ${className}`}\n  data-oid=\"gallery-container\">\n    {children}\n  </div>;\n\nconst SectionHeading = ({\n  kicker,\n  title,\n  subtitle\n}: {kicker?: string;title: string;subtitle?: string;}) =>\n<div className=\"mb-8 text-center\" data-oid=\"gallery-section-heading\">\n    {kicker ?\n  <p\n    className=\"text-xs font-semibold uppercase tracking-widest text-teal-600\"\n    data-oid=\"gallery-kicker\">\n        {kicker}\n      </p> :\n  null}\n    <h2\n    className=\"mt-2 text-3xl font-semibold tracking-tight text-gray-900 sm:text-4xl\"\n    data-oid=\"gallery-title\">\n      {title}\n    </h2>\n    {subtitle ?\n  <p\n    className=\"mx-auto mt-3 max-w-3xl text-base text-gray-700\"\n    data-oid=\"gallery-subtitle\">\n        {subtitle}\n      </p> :\n  null}\n  </div>;\n\n// Contact constants\nconst PHONE = \"************\";\nconst EMAIL = \"<EMAIL>\";\n\n// Header component matching homepage style\nfunction Header() {\n  return (\n    <header\n      className=\"sticky top-0 z-50 border-b border-gray-200/70 bg-white/80 backdrop-blur supports-[backdrop-filter]:bg-white/60\"\n      data-oid=\"gallery-header\">\n      <Container\n        className=\"flex items-center justify-between py-3\"\n        data-oid=\"gallery-header-container\">\n        <Link\n          href=\"/\"\n          className=\"flex items-center gap-3\"\n          aria-label=\"Wilkins Carpet Cleaning Logo\"\n          data-oid=\"gallery-logo\">\n          <div\n            className=\"grid h-9 w-9 place-content-center rounded-md bg-teal-600 text-white\"\n            data-oid=\"gallery-logo-icon\">\n            <span className=\"text-sm font-bold\" data-oid=\"gallery-logo-text\">\n              W\n            </span>\n          </div>\n          <div className=\"leading-tight\" data-oid=\"gallery-logo-text-container\">\n            <p\n              className=\"text-sm font-semibold text-gray-900\"\n              data-oid=\"gallery-company-name\">\n              Wilkins Carpet Cleaning\n            </p>\n            <p className=\"text-xs text-gray-600\" data-oid=\"gallery-tagline\">\n              Family-Owned Service Since 2003\n            </p>\n          </div>\n        </Link>\n        <nav\n          className=\"hidden items-center gap-6 text-sm font-medium text-gray-700 md:flex\"\n          data-oid=\"gallery-nav\">\n          <Link href=\"/#services\" className=\"hover:text-teal-700\" data-oid=\"gallery-nav-services\">\n            Services\n          </Link>\n          <Link href=\"/#about\" className=\"hover:text-teal-700\" data-oid=\"gallery-nav-about\">\n            About\n          </Link>\n          <Link href=\"/gallery\" className=\"text-teal-700 font-semibold\" data-oid=\"gallery-nav-gallery\">\n            Gallery\n          </Link>\n          <Link href=\"/#reviews\" className=\"hover:text-teal-700\" data-oid=\"gallery-nav-reviews\">\n            Reviews\n          </Link>\n          <Link href=\"/blog/why-wilkins\" className=\"hover:text-teal-700\" data-oid=\"gallery-nav-why\">\n            Why Wilkins?\n          </Link>\n          <Link href=\"/#contact\" className=\"hover:text-teal-700\" data-oid=\"gallery-nav-contact\">\n            Contact\n          </Link>\n        </nav>\n        <div className=\"flex items-center gap-2\" data-oid=\"gallery-header-cta\">\n          <a\n            href={`tel:${PHONE}`}\n            className=\"inline-flex items-center rounded-md bg-teal-600 px-3 py-2 text-sm font-semibold text-white shadow-sm ring-1 ring-inset ring-teal-700/10 transition hover:bg-teal-700\"\n            data-oid=\"gallery-call-button\">\n            Call Now\n          </a>\n        </div>\n      </Container>\n    </header>\n  );\n}\n\n// Gallery data with before/after images\nconst galleryProjects = [\n  {\n    id: 1,\n    title: \"Living Room Carpet Transformation\",\n    description: \"Heavy traffic area restored to like-new condition using our 12-step cleaning process. This carpet was heavily soiled from years of foot traffic and pet accidents.\",\n    category: \"Residential\",\n    beforeImage: \"https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/L5t2LlW1L90yYr78gOVn/media/68cc5a3409fa3e9db0b40e4d.jpeg\",\n    afterImage: \"https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/L5t2LlW1L90yYr78gOVn/media/68cc5a344fae2c19abdd1210.jpeg\"\n  },\n  {\n    id: 2,\n    title: \"Commercial Office Deep Clean\",\n    description: \"Professional office carpet cleaning with minimal business disruption. Years of coffee spills and daily wear transformed back to professional appearance.\",\n    category: \"Commercial\",\n    beforeImage: \"https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/L5t2LlW1L90yYr78gOVn/media/68cc5a34b800037b792a3a4a.jpeg\",\n    afterImage: \"https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/L5t2LlW1L90yYr78gOVn/media/68cc5a34d74f6b28ab5dd12e.jpeg\"\n  },\n  {\n    id: 3,\n    title: \"Stain Removal Success Story\",\n    description: \"Tough stains that other companies said couldn't be cleaned - we proved them wrong! Deep-set stains from food, drinks, and pets completely eliminated.\",\n    category: \"Stain Removal\",\n    beforeImage: \"https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/L5t2LlW1L90yYr78gOVn/media/68cc5a34eaa0586d7ca94628.jpeg\",\n    afterImage: \"https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/L5t2LlW1L90yYr78gOVn/media/68cc5a34f782ba7833b4bfdb.jpeg\"\n  },\n  {\n    id: 4,\n    title: \"Bedroom Carpet Restoration\",\n    description: \"Master bedroom carpet that looked beyond saving brought back to life. Our advanced cleaning techniques removed years of accumulated dirt and allergens.\",\n    category: \"Residential\",\n    beforeImage: \"https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/L5t2LlW1L90yYr78gOVn/media/68cc5a34eaa0586d7ca94628.jpeg\",\n    afterImage: \"https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/L5t2LlW1L90yYr78gOVn/media/68cc5a3409fa3e9db0b40e4d.jpeg\"\n  },\n  {\n    id: 5,\n    title: \"Restaurant Carpet Deep Clean\",\n    description: \"High-traffic restaurant carpet cleaned during off-hours. Food stains, grease, and heavy soiling completely removed without disrupting business operations.\",\n    category: \"Commercial\",\n    beforeImage: \"https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/L5t2LlW1L90yYr78gOVn/media/68cc5a34f782ba7833b4bfdb.jpeg\",\n    afterImage: \"https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/L5t2LlW1L90yYr78gOVn/media/68cc5a344fae2c19abdd1210.jpeg\"\n  }\n];\n\n// Breadcrumb component\nfunction Breadcrumb() {\n  return (\n    <div className=\"bg-gray-50 border-b border-gray-200\" data-oid=\"gallery-breadcrumb\">\n      <Container className=\"py-4\" data-oid=\"gallery-breadcrumb-container\">\n        <nav className=\"flex\" aria-label=\"Breadcrumb\" data-oid=\"gallery-breadcrumb-nav\">\n          <ol className=\"flex items-center space-x-2 text-sm text-gray-500\">\n            <li data-oid=\"gallery-breadcrumb-home\">\n              <Link href=\"/\" className=\"hover:text-teal-600 transition-colors\">\n                Home\n              </Link>\n            </li>\n            <li data-oid=\"gallery-breadcrumb-separator\">\n              <svg className=\"w-4 h-4 text-gray-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\n              </svg>\n            </li>\n            <li className=\"text-gray-900 font-medium\" data-oid=\"gallery-breadcrumb-current\">\n              Gallery\n            </li>\n          </ol>\n        </nav>\n      </Container>\n    </div>\n  );\n}\n\n// Main Gallery Section\nfunction GallerySection() {\n  return (\n    <section\n      className=\"bg-white py-16 sm:py-20\"\n      data-oid=\"main-gallery-section\">\n      <Container data-oid=\"main-gallery-container\">\n        <SectionHeading\n          kicker=\"Before & After Gallery\"\n          title=\"See the Amazing Transformations\"\n          subtitle=\"Real results from our satisfied customers across Rocky Mount, Wilson, and surrounding areas. These dramatic before and after comparisons show the power of our professional cleaning process.\"\n          data-oid=\"main-gallery-heading\" />\n\n        <div\n          className=\"grid grid-cols-1 gap-12 lg:gap-16\"\n          data-oid=\"gallery-projects-grid\">\n          {galleryProjects.map((project) => (\n            <div\n              key={project.id}\n              className=\"rounded-xl border border-gray-200 bg-white p-6 shadow-sm\"\n              data-oid={`gallery-project-${project.id}`}>\n              \n              {/* Project Header */}\n              <div className=\"mb-6 text-center\">\n                <div\n                  className=\"inline-flex items-center rounded-full bg-teal-600/10 px-3 py-1 text-xs font-medium text-teal-700 ring-1 ring-teal-600/20 mb-3\"\n                  data-oid={`project-category-${project.id}`}>\n                  {project.category}\n                </div>\n                <h3\n                  className=\"text-xl font-semibold text-gray-900 mb-2\"\n                  data-oid={`project-title-${project.id}`}>\n                  {project.title}\n                </h3>\n                <p\n                  className=\"text-sm text-gray-700 max-w-2xl mx-auto\"\n                  data-oid={`project-description-${project.id}`}>\n                  {project.description}\n                </p>\n              </div>\n\n              {/* Before/After Slider */}\n              <BeforeAfterSlider\n                beforeImage={project.beforeImage}\n                afterImage={project.afterImage}\n                beforeLabel=\"Before\"\n                afterLabel=\"After\"\n                data-oid={`slider-${project.id}`}\n              />\n            </div>\n          ))}\n        </div>\n\n        {/* Call to Action */}\n        <div className=\"mt-16 text-center\" data-oid=\"gallery-cta-section\">\n          <div className=\"rounded-xl border border-gray-200 bg-gray-50 p-8\">\n            <h3 className=\"text-2xl font-semibold text-gray-900 mb-4\">\n              Ready for Your Own Amazing Results?\n            </h3>\n            <p className=\"text-gray-700 mb-6 max-w-2xl mx-auto\">\n              Don't wait until your carpets are beyond help. Get your FREE estimate today and discover why customers say we're the best in the business.\n            </p>\n            <div className=\"flex flex-col gap-3 sm:flex-row sm:justify-center\">\n              <a\n                href={`tel:${PHONE}`}\n                className=\"inline-flex items-center justify-center rounded-md bg-teal-600 px-6 py-3 text-sm font-semibold text-white shadow-sm ring-1 ring-inset ring-teal-700/10 transition hover:bg-teal-700\"\n                data-oid=\"gallery-cta-phone\">\n                Call {PHONE} for FREE Estimate\n              </a>\n              <a\n                href={`mailto:${EMAIL}`}\n                className=\"inline-flex items-center justify-center rounded-md bg-white px-6 py-3 text-sm font-semibold text-teal-700 ring-1 ring-inset ring-teal-600/20 transition hover:bg-teal-50\"\n                data-oid=\"gallery-cta-email\">\n                Get Written Estimate\n              </a>\n            </div>\n          </div>\n        </div>\n      </Container>\n    </section>\n  );\n}\n\n// Footer component matching homepage style\nfunction Footer() {\n  return (\n    <footer className=\"border-t border-gray-200 bg-gray-50\" data-oid=\"gallery-footer\">\n      <Container className=\"py-10\" data-oid=\"gallery-footer-container\">\n        <div\n          className=\"grid grid-cols-1 gap-8 sm:grid-cols-3\"\n          data-oid=\"gallery-footer-grid\">\n          <div data-oid=\"gallery-footer-brand\">\n            <div className=\"flex items-center gap-3\" data-oid=\"gallery-footer-logo\">\n              <div\n                className=\"grid h-9 w-9 place-content-center rounded-md bg-teal-600 text-white\"\n                data-oid=\"gallery-footer-logo-icon\">\n                <span className=\"text-sm font-bold\" data-oid=\"gallery-footer-logo-text\">\n                  W\n                </span>\n              </div>\n              <div className=\"leading-tight\" data-oid=\"gallery-footer-brand-text\">\n                <p\n                  className=\"text-sm font-semibold text-gray-900\"\n                  data-oid=\"gallery-footer-company-name\">\n                  Wilkins Carpet Cleaning\n                </p>\n                <p className=\"text-xs text-gray-600\" data-oid=\"gallery-footer-tagline\">\n                  Family-Owned Service Since 2003\n                </p>\n              </div>\n            </div>\n            <p className=\"mt-4 text-sm text-gray-700\" data-oid=\"gallery-footer-description\">\n              Carolina's #1 preferred carpet cleaning specialist. Family-owned\n              and operated, serving the Rocky Mount area with honest, dependable\n              service for over 20 years.\n            </p>\n          </div>\n          <div data-oid=\"gallery-footer-services\">\n            <h4\n              className=\"text-sm font-semibold text-gray-900\"\n              data-oid=\"gallery-footer-services-title\">\n              Services\n            </h4>\n            <ul\n              className=\"mt-3 space-y-1 text-sm text-gray-700\"\n              data-oid=\"gallery-footer-services-list\">\n              <li data-oid=\"gallery-footer-service-1\">Carpet Steam Cleaning</li>\n              <li data-oid=\"gallery-footer-service-2\">Commercial Carpet Cleaning</li>\n              <li data-oid=\"gallery-footer-service-3\">Upholstery Cleaning</li>\n              <li data-oid=\"gallery-footer-service-4\">Tile & Grout Cleaning</li>\n              <li data-oid=\"gallery-footer-service-5\">Carpet Protection</li>\n              <li data-oid=\"gallery-footer-service-6\">Janitorial Services</li>\n            </ul>\n          </div>\n          <div data-oid=\"gallery-footer-contact\">\n            <h4\n              className=\"text-sm font-semibold text-gray-900\"\n              data-oid=\"gallery-footer-contact-title\">\n              Contact\n            </h4>\n            <ul\n              className=\"mt-3 space-y-1 text-sm text-gray-700\"\n              data-oid=\"gallery-footer-contact-list\">\n              <li data-oid=\"gallery-footer-phone\">\n                <a\n                  className=\"text-teal-700 underline-offset-2 hover:underline\"\n                  href={`tel:${PHONE}`}\n                  data-oid=\"gallery-footer-phone-link\">\n                  {PHONE}\n                </a>{\" \"}\n                (Call or text anytime)\n              </li>\n              <li data-oid=\"gallery-footer-email\">\n                <a\n                  className=\"text-teal-700 underline-offset-2 hover:underline\"\n                  href={`mailto:${EMAIL}`}\n                  data-oid=\"gallery-footer-email-link\">\n                  {EMAIL}\n                </a>\n              </li>\n              <li data-oid=\"gallery-footer-areas\">\n                Service Areas: Rocky Mount, Wilson, Tarboro, Nashville, and\n                surrounding areas in North Carolina.\n              </li>\n            </ul>\n          </div>\n        </div>\n        <div\n          className=\"mt-10 flex flex-col items-center justify-between gap-4 border-t border-gray-200 pt-6 text-xs text-gray-600 sm:flex-row\"\n          data-oid=\"gallery-footer-bottom\">\n          <span data-oid=\"gallery-footer-copyright\">\n            © {new Date().getFullYear()} Wilkins Carpet Cleaning. All rights\n            reserved.\n          </span>\n          <span data-oid=\"gallery-footer-credits\">\n            <Link href=\"/\" className=\"hover:text-teal-600\">← Back to Home</Link> • Rating: 4.8/5 Stars with 50+ Google Reviews\n          </span>\n        </div>\n      </Container>\n    </footer>\n  );\n}\n\nexport default function GalleryPage() {\n  return (\n    <div\n      className=\"w-full min-h-screen scroll-smooth bg-white text-gray-900\"\n      data-oid=\"gallery-page\">\n      <Header data-oid=\"gallery-page-header\" />\n      <Breadcrumb data-oid=\"gallery-page-breadcrumb\" />\n      <main data-oid=\"gallery-page-main\">\n        <GallerySection data-oid=\"gallery-page-section\" />\n      </main>\n      <Footer data-oid=\"gallery-page-footer\" />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,wDAAwD;AACxD,MAAM,YAAY,CAAC,EACjB,QAAQ,EACR,YAAY,EAAE,EACkC,iBAClD,8OAAC;QACC,WAAW,CAAC,8CAA8C,EAAE,WAAW;QACvE,YAAS;kBACN;;;;;;AAGL,MAAM,iBAAiB,CAAC,EACtB,MAAM,EACN,KAAK,EACL,QAAQ,EAC2C,iBACrD,8OAAC;QAAI,WAAU;QAAmB,YAAS;;YACtC,uBACH,8OAAC;gBACC,WAAU;gBACV,YAAS;0BACJ;;;;;2DAEP;0BACE,8OAAC;gBACD,WAAU;gBACV,YAAS;0BACN;;;;;;YAEF,yBACH,8OAAC;gBACC,WAAU;gBACV,YAAS;0BACJ;;;;;2DAEP;;;;;;;AAGF,oBAAoB;AACpB,MAAM,QAAQ;AACd,MAAM,QAAQ;AAEd,2CAA2C;AAC3C,SAAS;IACP,qBACE,8OAAC;QACC,WAAU;QACV,YAAS;kBACT,cAAA,8OAAC;YACC,WAAU;YACV,YAAS;;8BACT,8OAAC,uKAAI;oBACH,MAAK;oBACL,WAAU;oBACV,cAAW;oBACX,YAAS;;sCACT,8OAAC;4BACC,WAAU;4BACV,YAAS;sCACT,cAAA,8OAAC;gCAAK,WAAU;gCAAoB,YAAS;0CAAoB;;;;;;;;;;;sCAInE,8OAAC;4BAAI,WAAU;4BAAgB,YAAS;;8CACtC,8OAAC;oCACC,WAAU;oCACV,YAAS;8CAAuB;;;;;;8CAGlC,8OAAC;oCAAE,WAAU;oCAAwB,YAAS;8CAAkB;;;;;;;;;;;;;;;;;;8BAKpE,8OAAC;oBACC,WAAU;oBACV,YAAS;;sCACT,8OAAC,uKAAI;4BAAC,MAAK;4BAAa,WAAU;4BAAsB,YAAS;sCAAuB;;;;;;sCAGxF,8OAAC,uKAAI;4BAAC,MAAK;4BAAU,WAAU;4BAAsB,YAAS;sCAAoB;;;;;;sCAGlF,8OAAC,uKAAI;4BAAC,MAAK;4BAAW,WAAU;4BAA8B,YAAS;sCAAsB;;;;;;sCAG7F,8OAAC,uKAAI;4BAAC,MAAK;4BAAY,WAAU;4BAAsB,YAAS;sCAAsB;;;;;;sCAGtF,8OAAC,uKAAI;4BAAC,MAAK;4BAAoB,WAAU;4BAAsB,YAAS;sCAAkB;;;;;;sCAG1F,8OAAC,uKAAI;4BAAC,MAAK;4BAAY,WAAU;4BAAsB,YAAS;sCAAsB;;;;;;;;;;;;8BAIxF,8OAAC;oBAAI,WAAU;oBAA0B,YAAS;8BAChD,cAAA,8OAAC;wBACC,MAAM,CAAC,IAAI,EAAE,OAAO;wBACpB,WAAU;wBACV,YAAS;kCAAsB;;;;;;;;;;;;;;;;;;;;;;AAO3C;AAEA,wCAAwC;AACxC,MAAM,kBAAkB;IACtB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,aAAa;QACb,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,aAAa;QACb,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,aAAa;QACb,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,aAAa;QACb,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,aAAa;QACb,YAAY;IACd;CACD;AAED,uBAAuB;AACvB,SAAS;IACP,qBACE,8OAAC;QAAI,WAAU;QAAsC,YAAS;kBAC5D,cAAA,8OAAC;YAAU,WAAU;YAAO,YAAS;sBACnC,cAAA,8OAAC;gBAAI,WAAU;gBAAO,cAAW;gBAAa,YAAS;0BACrD,cAAA,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC;4BAAG,YAAS;sCACX,cAAA,8OAAC,uKAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAwC;;;;;;;;;;;sCAInE,8OAAC;4BAAG,YAAS;sCACX,cAAA,8OAAC;gCAAI,WAAU;gCAAwB,MAAK;gCAAe,SAAQ;0CACjE,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAqH,UAAS;;;;;;;;;;;;;;;;sCAG7J,8OAAC;4BAAG,WAAU;4BAA4B,YAAS;sCAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5F;AAEA,uBAAuB;AACvB,SAAS;IACP,qBACE,8OAAC;QACC,WAAU;QACV,YAAS;kBACT,cAAA,8OAAC;YAAU,YAAS;;8BAClB,8OAAC;oBACC,QAAO;oBACP,OAAM;oBACN,UAAS;oBACT,YAAS;;;;;;8BAEX,8OAAC;oBACC,WAAU;oBACV,YAAS;8BACR,gBAAgB,GAAG,CAAC,CAAC,wBACpB,8OAAC;4BAEC,WAAU;4BACV,YAAU,CAAC,gBAAgB,EAAE,QAAQ,EAAE,EAAE;;8CAGzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAU;4CACV,YAAU,CAAC,iBAAiB,EAAE,QAAQ,EAAE,EAAE;sDACzC,QAAQ,QAAQ;;;;;;sDAEnB,8OAAC;4CACC,WAAU;4CACV,YAAU,CAAC,cAAc,EAAE,QAAQ,EAAE,EAAE;sDACtC,QAAQ,KAAK;;;;;;sDAEhB,8OAAC;4CACC,WAAU;4CACV,YAAU,CAAC,oBAAoB,EAAE,QAAQ,EAAE,EAAE;sDAC5C,QAAQ,WAAW;;;;;;;;;;;;8CAKxB,8OAAC,kJAAiB;oCAChB,aAAa,QAAQ,WAAW;oCAChC,YAAY,QAAQ,UAAU;oCAC9B,aAAY;oCACZ,YAAW;oCACX,YAAU,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;;;;;;;2BA7B7B,QAAQ,EAAE;;;;;;;;;;8BAoCrB,8OAAC;oBAAI,WAAU;oBAAoB,YAAS;8BAC1C,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4C;;;;;;0CAG1D,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAGpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAM,CAAC,IAAI,EAAE,OAAO;wCACpB,WAAU;wCACV,YAAS;;4CAAoB;4CACvB;4CAAM;;;;;;;kDAEd,8OAAC;wCACC,MAAM,CAAC,OAAO,EAAE,OAAO;wCACvB,WAAU;wCACV,YAAS;kDAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7C;AAEA,2CAA2C;AAC3C,SAAS;IACP,qBACE,8OAAC;QAAO,WAAU;QAAsC,YAAS;kBAC/D,cAAA,8OAAC;YAAU,WAAU;YAAQ,YAAS;;8BACpC,8OAAC;oBACC,WAAU;oBACV,YAAS;;sCACT,8OAAC;4BAAI,YAAS;;8CACZ,8OAAC;oCAAI,WAAU;oCAA0B,YAAS;;sDAChD,8OAAC;4CACC,WAAU;4CACV,YAAS;sDACT,cAAA,8OAAC;gDAAK,WAAU;gDAAoB,YAAS;0DAA2B;;;;;;;;;;;sDAI1E,8OAAC;4CAAI,WAAU;4CAAgB,YAAS;;8DACtC,8OAAC;oDACC,WAAU;oDACV,YAAS;8DAA8B;;;;;;8DAGzC,8OAAC;oDAAE,WAAU;oDAAwB,YAAS;8DAAyB;;;;;;;;;;;;;;;;;;8CAK3E,8OAAC;oCAAE,WAAU;oCAA6B,YAAS;8CAA6B;;;;;;;;;;;;sCAMlF,8OAAC;4BAAI,YAAS;;8CACZ,8OAAC;oCACC,WAAU;oCACV,YAAS;8CAAgC;;;;;;8CAG3C,8OAAC;oCACC,WAAU;oCACV,YAAS;;sDACT,8OAAC;4CAAG,YAAS;sDAA2B;;;;;;sDACxC,8OAAC;4CAAG,YAAS;sDAA2B;;;;;;sDACxC,8OAAC;4CAAG,YAAS;sDAA2B;;;;;;sDACxC,8OAAC;4CAAG,YAAS;sDAA2B;;;;;;sDACxC,8OAAC;4CAAG,YAAS;sDAA2B;;;;;;sDACxC,8OAAC;4CAAG,YAAS;sDAA2B;;;;;;;;;;;;;;;;;;sCAG5C,8OAAC;4BAAI,YAAS;;8CACZ,8OAAC;oCACC,WAAU;oCACV,YAAS;8CAA+B;;;;;;8CAG1C,8OAAC;oCACC,WAAU;oCACV,YAAS;;sDACT,8OAAC;4CAAG,YAAS;;8DACX,8OAAC;oDACC,WAAU;oDACV,MAAM,CAAC,IAAI,EAAE,OAAO;oDACpB,YAAS;8DACR;;;;;;gDACE;gDAAI;;;;;;;sDAGX,8OAAC;4CAAG,YAAS;sDACX,cAAA,8OAAC;gDACC,WAAU;gDACV,MAAM,CAAC,OAAO,EAAE,OAAO;gDACvB,YAAS;0DACR;;;;;;;;;;;sDAGL,8OAAC;4CAAG,YAAS;sDAAuB;;;;;;;;;;;;;;;;;;;;;;;;8BAO1C,8OAAC;oBACC,WAAU;oBACV,YAAS;;sCACT,8OAAC;4BAAK,YAAS;;gCAA2B;gCACrC,IAAI,OAAO,WAAW;gCAAG;;;;;;;sCAG9B,8OAAC;4BAAK,YAAS;;8CACb,8OAAC,uKAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAsB;;;;;;gCAAqB;;;;;;;;;;;;;;;;;;;;;;;;AAMhF;AAEe,SAAS;IACtB,qBACE,8OAAC;QACC,WAAU;QACV,YAAS;;0BACT,8OAAC;gBAAO,YAAS;;;;;;0BACjB,8OAAC;gBAAW,YAAS;;;;;;0BACrB,8OAAC;gBAAK,YAAS;0BACb,cAAA,8OAAC;oBAAe,YAAS;;;;;;;;;;;0BAE3B,8OAAC;gBAAO,YAAS;;;;;;;;;;;;AAGvB", "debugId": null}}]}